<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络拓扑图</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            font-family: Arial, sans-serif;
            overflow-x: auto;
        }
        
        .topology-container {
            width: 1400px;
            height: 750px;
            position: relative;
            background: rgba(0, 20, 40, 0.8);
            border-radius: 10px;
            padding: 20px;
        }
        
        .device {
            position: absolute;
            background: #f0f0f0;
            border: 2px solid #ccc;
            border-radius: 8px;
            text-align: center;
            font-size: 12px;
            font-weight: bold;
            color: #333;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 10;
        }

        .device:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            border-color: #4CAF50;
        }
        
        .core-switch {
            width: 80px;
            height: 40px;
            background: linear-gradient(135deg, #e8e8e8, #d0d0d0);
        }
        
        .aggregation-switch {
            width: 70px;
            height: 35px;
            background: linear-gradient(135deg, #e8e8e8, #d0d0d0);
        }
        
        .access-switch {
            width: 60px;
            height: 30px;
            background: linear-gradient(135deg, #e8e8e8, #d0d0d0);
        }
        
        .terminal {
            width: 50px;
            height: 25px;
            background: linear-gradient(135deg, #e8e8e8, #d0d0d0);
            font-size: 10px;
        }
        
        .status-indicator {
            position: absolute;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            top: -6px;
            left: -6px;
        }
        
        .status-green {
            background: #4CAF50;
            box-shadow: 0 0 8px #4CAF50;
        }
        
        .status-gray {
            background: #9E9E9E;
            box-shadow: 0 0 8px #9E9E9E;
        }
        
        .connection-line {
            position: absolute;
            background: linear-gradient(90deg, #4CAF50, #2196F3);
            transform-origin: left center;
            opacity: 0.6;
            box-shadow: 0 0 2px rgba(76, 175, 80, 0.3);
        }

        .connection-line.terminal-connection {
            background: #666;
            opacity: 0.4;
            box-shadow: none;
        }
        
        .legend {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-size: 12px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin: 5px 0;
        }
        
        .legend-icon {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .topology-title {
            position: absolute;
            top: 20px;
            right: 20px;
            color: white;
            font-size: 24px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .layer-label {
            position: absolute;
            left: 10px;
            color: #4CAF50;
            font-weight: bold;
            font-size: 14px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        }
    </style>
</head>
<body>
    <div class="topology-container">
        <!-- 标题 -->
        <div class="topology-title">网络拓扑图</div>

        <!-- 图例 -->
        <div class="legend">
            <div class="legend-item">
                <div class="legend-icon status-green"></div>
                <span>网关设备人员进出权限管理系统正常运行</span>
            </div>
            <div class="legend-item">
                <div class="legend-icon status-gray"></div>
                <span>网关设备人员进出权限管理系统异常</span>
            </div>
            <div class="legend-item">
                <div class="legend-icon status-green"></div>
                <span>网关设备/设备网关数据传输正常运行</span>
            </div>
            <div class="legend-item">
                <div class="legend-icon status-gray"></div>
                <span>网关设备/设备网关异常</span>
            </div>
        </div>
        
        <!-- 核心层设备 -->
        <div class="layer-label" style="top: 55px;">核心层</div>
        <div class="device core-switch" style="top: 50px; left: 200px;">
            <div class="status-indicator status-green"></div>
            网关设备 1#
        </div>
        <div class="device core-switch" style="top: 50px; left: 320px;">
            <div class="status-indicator status-gray"></div>
            网关设备 2#
        </div>
        <div class="device core-switch" style="top: 50px; left: 440px;">
            <div class="status-indicator status-gray"></div>
            网关设备 3#
        </div>
        <div class="device core-switch" style="top: 50px; left: 560px;">
            <div class="status-indicator status-gray"></div>
            网关设备 4#
        </div>
        <div class="device core-switch" style="top: 50px; left: 680px;">
            <div class="status-indicator status-gray"></div>
            网关设备 5#
        </div>
        <div class="device core-switch" style="top: 50px; left: 800px;">
            <div class="status-indicator status-gray"></div>
            网关设备 6#
        </div>
        
        <!-- 汇聚层设备 -->
        <div class="layer-label" style="top: 155px;">汇聚层</div>
        <div class="device aggregation-switch" style="top: 150px; left: 320px;">
            <div class="status-indicator status-gray"></div>
            网关设备 1#
        </div>
        <div class="device aggregation-switch" style="top: 150px; left: 440px;">
            <div class="status-indicator status-gray"></div>
            网关设备 2#
        </div>
        <div class="device aggregation-switch" style="top: 150px; left: 560px;">
            <div class="status-indicator status-gray"></div>
            网关设备 3#
        </div>
        <div class="device aggregation-switch" style="top: 150px; left: 680px;">
            <div class="status-indicator status-gray"></div>
            网关设备 4#
        </div>
        
        <!-- 接入层设备行 -->
        <div class="layer-label" style="top: 255px;">接入层</div>
        <div id="access-layer" style="top: 250px;"></div>

        <!-- 终端设备 -->
        <div class="layer-label" style="top: 405px;">终端设备</div>
        <div id="terminal-layer" style="top: 350px;"></div>
    </div>
    
    <script>
        // 生成接入层设备
        function generateAccessLayer() {
            // 接入层设备，包含7091、7092、7093
            const devices = [
                '7001', '7002', '7003', '7101', '7102', '7103', '7H', '7H2', '7H3', '7H4', '7H5', '7H6', '7H7', '7H8', '7H9', '7H10', '7H11', '7H12', '7H13', '7H14', '7H15', '7H16', '7H17', '7H18', '7H19', '7H20', '7H21', '7H22', '7H23', '7H24', '7H25', '7H26', '7H27', '7H28', '7H29', '7H30', '7091', '7092', '7093'
            ];

            devices.forEach((device, index) => {
                const deviceEl = document.createElement('div');
                deviceEl.className = 'device access-switch';
                deviceEl.style.left = (50 + index * 35) + 'px';
                deviceEl.style.top = '250px';
                deviceEl.id = device; // 添加ID便于后续连接

                const indicator = document.createElement('div');
                indicator.className = 'status-indicator ' + (Math.random() > 0.5 ? 'status-green' : 'status-gray');

                deviceEl.appendChild(indicator);
                deviceEl.appendChild(document.createTextNode(device));

                document.querySelector('.topology-container').appendChild(deviceEl);
            });

            // 添加多个碎料仓（每3个接入设备对应1个碎料仓）
            const totalAccessDevices = 39; // 总共39个接入设备
            const devicesPerGroup = 3; // 每组3个设备
            const groupCount = Math.ceil(totalAccessDevices / devicesPerGroup); // 13组

            for (let groupIndex = 0; groupIndex < groupCount; groupIndex++) {
                const suiliaocanEl = document.createElement('div');
                suiliaocanEl.className = 'device aggregation-switch';
                suiliaocanEl.style.left = (67 + groupIndex * 105) + 'px'; // 每组间隔105px
                suiliaocanEl.style.top = '350px';
                suiliaocanEl.style.width = '80px';
                suiliaocanEl.id = 'suiliaocang' + (groupIndex + 1);

                const indicator = document.createElement('div');
                indicator.className = 'status-indicator status-green';

                suiliaocanEl.appendChild(indicator);
                suiliaocanEl.appendChild(document.createTextNode('碎料仓' + (groupIndex + 1)));

                document.querySelector('.topology-container').appendChild(suiliaocanEl);

                // 为每个碎料仓添加5个车间
                for (let workshopIndex = 0; workshopIndex < 5; workshopIndex++) {
                    const workshopEl = document.createElement('div');
                    workshopEl.className = 'device terminal';
                    workshopEl.style.left = (67 + groupIndex * 105) + 'px'; // 与碎料仓对齐
                    workshopEl.style.top = (450 + workshopIndex * 40) + 'px'; // 垂直排列
                    workshopEl.style.width = '60px';
                    workshopEl.id = 'workshop' + groupIndex + '_' + (workshopIndex + 1);

                    const indicator = document.createElement('div');
                    indicator.className = 'status-indicator status-green';

                    workshopEl.appendChild(indicator);
                    workshopEl.appendChild(document.createTextNode('车间' + (groupIndex * 5 + workshopIndex + 1)));

                    document.querySelector('.topology-container').appendChild(workshopEl);
                }
            }
        }
        
        // 生成终端设备
        function generateTerminalLayer() {
            const terminals = ['控制台', '1号机', '2号机', '3号机', '4号机', '5号机', '6号机', '7号机', '8号机', '9号机', '10号机'];

            terminals.forEach((terminal, colIndex) => {
                for (let rowIndex = 0; rowIndex < 5; rowIndex++) {
                    const terminalEl = document.createElement('div');
                    terminalEl.className = 'device terminal';
                    terminalEl.style.left = (50 + colIndex * 120) + 'px';
                    terminalEl.style.top = (400 + rowIndex * 35) + 'px';

                    const indicator = document.createElement('div');
                    indicator.className = 'status-indicator ' + (Math.random() > 0.3 ? 'status-green' : 'status-gray');

                    terminalEl.appendChild(indicator);
                    terminalEl.appendChild(document.createTextNode(terminal));

                    document.querySelector('.topology-container').appendChild(terminalEl);
                }
            });
        }

        // 绘制连接线
        function drawConnections() {
            const container = document.querySelector('.topology-container');

            // 核心层设备坐标
            const coreDevices = [
                {x: 240, y: 90}, {x: 360, y: 90}, {x: 480, y: 90},
                {x: 600, y: 90}, {x: 720, y: 90}, {x: 840, y: 90}
            ];

            // 汇聚层设备坐标
            const aggDevices = [
                {x: 355, y: 150}, {x: 475, y: 150}, {x: 595, y: 150}, {x: 715, y: 150}
            ];

            // 核心层到汇聚层：每2个核心设备汇聚到1个汇聚设备
            for (let i = 0; i < aggDevices.length; i++) {
                // 每个汇聚设备连接1-2个核心设备
                const startCore = Math.floor(i * 1.5);
                const endCore = Math.min(startCore + 2, coreDevices.length);

                for (let j = startCore; j < endCore; j++) {
                    drawRightAngleLine(coreDevices[j].x, coreDevices[j].y, aggDevices[i].x, aggDevices[i].y);
                }
            }

            // 汇聚层到接入层：每个汇聚设备连接多个接入设备
            for (let i = 0; i < aggDevices.length; i++) {
                const startAccess = i * 9; // 每个汇聚设备管理9个接入设备
                const endAccess = Math.min(startAccess + 9, 39); // 总共39个接入设备

                for (let j = startAccess; j < endAccess; j++) {
                    const accessX = 67 + j * 35;
                    const accessY = 250;
                    const aggX = aggDevices[i].x;
                    const aggY = aggDevices[i].y + 35;

                    drawRightAngleLine(aggX, aggY, accessX, accessY);
                }
            }

            // 接入层设备每3个连接到一个碎料仓
            const totalAccessDevices = 39;
            const devicesPerGroup = 3;
            const groupCount = Math.ceil(totalAccessDevices / devicesPerGroup);

            for (let groupIndex = 0; groupIndex < groupCount; groupIndex++) {
                const suiliaocanX = 107 + groupIndex * 105; // 碎料仓X坐标
                const suiliaocanY = 350; // 碎料仓Y坐标

                // 连接该组的3个接入设备到碎料仓
                for (let deviceInGroup = 0; deviceInGroup < devicesPerGroup; deviceInGroup++) {
                    const deviceIndex = groupIndex * devicesPerGroup + deviceInGroup;
                    if (deviceIndex < totalAccessDevices) {
                        const deviceX = 67 + deviceIndex * 35;
                        const deviceY = 280; // 接入层设备底部

                        drawRightAngleLine(deviceX, deviceY, suiliaocanX, suiliaocanY);
                    }
                }

                // 每个碎料仓垂直连接到5个车间
                for (let workshopIndex = 0; workshopIndex < 5; workshopIndex++) {
                    const workshopX = suiliaocanX; // 与碎料仓对齐
                    const workshopY = 450 + workshopIndex * 40;
                    const suiliaocanBottomY = 385; // 碎料仓底部

                    drawRightAngleLine(suiliaocanX, suiliaocanBottomY, workshopX, workshopY);
                }
            }
        }

        // 绘制横平竖直连接线的辅助函数
        function drawRightAngleLine(x1, y1, x2, y2, isTerminalConnection = false) {
            // 计算中间点（垂直方向的中点）
            const midY = y1 + (y2 - y1) / 2;
            const lineClass = isTerminalConnection ? 'connection-line terminal-connection' : 'connection-line';

            // 绘制第一段：垂直线（从起点到中间点）
            const verticalLine1 = document.createElement('div');
            verticalLine1.className = lineClass;
            verticalLine1.style.width = '2px';
            verticalLine1.style.height = Math.abs(midY - y1) + 'px';
            verticalLine1.style.left = x1 + 'px';
            verticalLine1.style.top = Math.min(y1, midY) + 'px';
            verticalLine1.style.zIndex = '1';
            document.querySelector('.topology-container').appendChild(verticalLine1);

            // 绘制第二段：水平线（从x1到x2）
            const horizontalLine = document.createElement('div');
            horizontalLine.className = lineClass;
            horizontalLine.style.width = Math.abs(x2 - x1) + 'px';
            horizontalLine.style.height = '2px';
            horizontalLine.style.left = Math.min(x1, x2) + 'px';
            horizontalLine.style.top = midY + 'px';
            horizontalLine.style.zIndex = '1';
            document.querySelector('.topology-container').appendChild(horizontalLine);

            // 绘制第三段：垂直线（从中间点到终点）
            const verticalLine2 = document.createElement('div');
            verticalLine2.className = lineClass;
            verticalLine2.style.width = '2px';
            verticalLine2.style.height = Math.abs(y2 - midY) + 'px';
            verticalLine2.style.left = x2 + 'px';
            verticalLine2.style.top = Math.min(midY, y2) + 'px';
            verticalLine2.style.zIndex = '1';
            document.querySelector('.topology-container').appendChild(verticalLine2);
        }
        
        // 初始化
        generateAccessLayer();
        generateTerminalLayer();
        drawConnections();
    </script>
</body>
</html>
